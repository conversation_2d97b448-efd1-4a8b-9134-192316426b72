import cv2
import easyocr
from open_image_models import LicensePlateDetector

# Load detector and OCR
lp_detector = LicensePlateDetector(detection_model="yolo-v9-t-640-license-plate-end2end")
ocr = easyocr.Reader(["en"])  # Supports many langs

# Open video
video_path = "input.mp4"
cap = cv2.VideoCapture(video_path)
fourcc = cv2.VideoWriter_fourcc(*"mp4v")
out = cv2.VideoWriter("annotated.mp4", fourcc, cap.get(cv2.CAP_PROP_FPS),
                      (int(cap.get(3)), int(cap.get(4))))

while True:
    ret, frame = cap.read()
    if not ret:
        break

    detections = lp_detector.predict(frame)
    for det in detections:
        x1, y1, x2, y2 = map(int, det.bbox)
        plate_crop = frame[y1:y2, x1:x2]
        ocr_result = ocr.readtext(plate_crop)

        # Draw detection
        cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
        if ocr_result:
            plate_text = ocr_result[0][1]
            cv2.putText(frame, plate_text, (x1, y1 - 10),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

    out.write(frame)

cap.release()
out.release()
cv2.destroyAllWindows()
