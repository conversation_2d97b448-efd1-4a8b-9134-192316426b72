import cv2
from open_image_models import LicensePlateDetector

# Use the YOLOv9 tiny 640 end-to-end license plate model.
lp_detector = LicensePlateDetector(detection_model="yolo-v9-t-640-license-plate-end2end")

# Load your image or a video frame
image = cv2.imread("../hard_example.png")

# Perform detection
detections = lp_detector.predict(image)
print(detections)

# To visualize results
annotated = lp_detector.display_predictions(image)
cv2.imshow("Plates Detected", annotated)
cv2.waitKey(0)
cv2.destroyAllWindows()
